import { ADDONS_EQUIPMENT, ADDONS_STATUS, ADDONS_TV } from "@constants";
import { Divider, Tabs, Grid, Tab, Typography, Paper, Box, Button } from "@mui/material";
import { IAddOns } from "@services/subscription/accountId/interface/IAddOns";
import { TContactDetails } from "@features/crm/CustomerDetails/ICustomerDetails";
import { useTranslation } from "react-i18next";
import { Fragment, useState } from "react";
import dayjs from "dayjs";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import { useAddonStore } from "@features/crm/SubscriptionsAddOns/useAddonStore";
import { Loader } from "@common";
import { ISubscriptionsCollection } from "@services/subscription/accountId/interface/ISubscriptionsAccountIds";
import { ITigoRiskManagementResponse } from "@modules/tigoSalesFacade/interfaces/responses/ITigoRiskManagementResponse";
import { useAddOnsCancellation } from "./useAddOnsCancellation";
import CancellationCart from "./CancellationCart";

interface IPropsMenuAddons {
    names: { name: string; label: string }[];
    addOnsServices: IAddOns | undefined;
    contactDetails: TContactDetails | undefined;
    setOrderIdForSchedule: React.Dispatch<React.SetStateAction<string>>;
    //Parametros para cancel addon
    subscriptions: ISubscriptionsCollection | undefined;
    setSuccessRisk: React.Dispatch<React.SetStateAction<ITigoRiskManagementResponse | null | undefined>>;
    setAddNewAddOns: React.Dispatch<React.SetStateAction<boolean>>;
}

const AddonsCancellation = ({
    names,
    addOnsServices,
    contactDetails,
    setOrderIdForSchedule,
    //Parametros para cancel addon
    subscriptions,
    setSuccessRisk,
    setAddNewAddOns,
}: IPropsMenuAddons) => {
    const { t } = useTranslation(["customer"]);
    const [tabValue, setTabValue] = useState(names[0]?.name ?? "");

    const handleTabChange = (_: React.SyntheticEvent, newValue: string) => {
        setTabValue(newValue);
    };

    const refreshAddons = useAddonStore((state) => state.refreshAddons);
    
    const {
        callWfeFacadeCancelationAddon,
        handleAddClick,
        handleRemoveClick,
        addedAddons,
    } = useAddOnsCancellation();

    return (
        <div>
            {refreshAddons ? (
                <Grid alignItems="center" container justifyContent="center" style={{ height: "30vh" }}>
                    <Loader />
                </Grid>
            ) : (
                <Grid container direction="row" spacing={3}>
                    <Grid item md={6} xs={12}>
                        <Tabs
                            aria-label="Add-ons tabs"
                            value={tabValue}
                            variant="fullWidth"
                            onChange={handleTabChange}
                        >
                            {names?.map(({ name, label }) => (
                                <Tab
                                    disabled={label === "INTERNET"}
                                    key={name}
                                    label={label}
                                    sx={{ fontSize: "14px" }}
                                    value={name}
                                />
                            ))}
                        </Tabs>
                        {addOnsServices?.collection.length !== 0 && addOnsServices !== undefined ? (
                            addOnsServices?.collection
                                .filter((val) => {
                                    if (tabValue === ADDONS_TV) {
                                        return (
                                            val.itemGroupCode?.includes(ADDONS_TV) ||
                                            val.itemGroupCode?.includes(ADDONS_EQUIPMENT)
                                        );
                                    }

                                    return val.itemGroupCode?.includes(tabValue);
                                })
                                .sort((a, b) => {
                                    return a.status === ADDONS_STATUS.ONGOING_TERMINATION
                                        ? 1
                                        : b.status === ADDONS_STATUS.ONGOING_TERMINATION
                                        ? -1
                                        : 0;
                                })
                                .map((element, index, array) => {
                                    const isFirstOngoingTermination =
                                        element.status === ADDONS_STATUS.ONGOING_TERMINATION &&
                                        array.findIndex((e) => e.status === ADDONS_STATUS.ONGOING_TERMINATION) === index;

                                    const canShowCancelButton = [
                                        ADDONS_STATUS.ACTIVE,
                                        ADDONS_STATUS.ON_GOING_REQUEST,
                                        ADDONS_STATUS.INITIAL,
                                        ADDONS_STATUS.NO_ONGOING_REQUEST,
                                    ].includes(element.status);

                                    const isInCart = addedAddons.some(addon => addon.id === element.id);

                                    return (
                                        <Fragment key={element.id}>
                                            {isFirstOngoingTermination && (
                                                <Divider sx={{ pt: 2, pb: 2 }}>
                                                    <Typography color="text.secondary" variant="subtitle2">
                                                        {t("customer:titleOngoingCancellationAddon")}
                                                    </Typography>
                                                </Divider>
                                            )}
                                            <Grid
                                                container
                                                direction="row"
                                                alignItems="center"
                                                sx={{
                                                    mb: 2,
                                                    p: 2,
                                                    border: isInCart ? "2px solid #ff5722" : "1px solid #e0e0e0",
                                                    borderRadius: 1,
                                                    backgroundColor: isInCart ? "#fff3e0" : "transparent"
                                                }}
                                            >
                                                <Grid item xs={4}>
                                                    <Typography variant="subtitle2" fontWeight="bold">
                                                        {element.description}
                                                    </Typography>
                                                    <Typography variant="body2" color="text.secondary">
                                                        ID: {element.id}
                                                    </Typography>
                                                    {isInCart && (
                                                        <Typography variant="caption" color="error" fontWeight="bold">
                                                            ✓ En carrito de cancelación
                                                        </Typography>
                                                    )}
                                                </Grid>
                                                <Grid item xs={3}>
                                                    <Typography variant="body2" color="text.secondary">
                                                        Fecha de activación
                                                    </Typography>
                                                    <Typography variant="body2">
                                                        {element.activatedAt ? dayjs(element.activatedAt).format("DD/MM/YYYY") : "-"}
                                                    </Typography>
                                                </Grid>
                                                <Grid item xs={3}>
                                                    <Typography variant="body2" color="text.secondary">
                                                        Serial
                                                    </Typography>
                                                    <Typography variant="body2">
                                                        {"SN1234567"+element.id}
                                                    </Typography>
                                                </Grid>
                                                <Grid item xs={2}>
                                                    {canShowCancelButton && element.status !== ADDONS_STATUS.ONGOING_TERMINATION && (
                                                        <Button
                                                            variant="contained"
                                                            color={isInCart ? "success" : "error"}
                                                            size="small"
                                                            onClick={() => handleAddClick(element)}
                                                            disabled={isInCart}
                                                            sx={{ fontSize: "12px" }}
                                                        >
                                                            {isInCart ? "Agregado" : "Cancelar"}
                                                        </Button>
                                                    )}
                                                </Grid>
                                            </Grid>
                                        </Fragment>
                                    );
                                })
                        ) : (
                            <Box
                                alignItems="center"
                                display="flex"
                                flexDirection="column"
                                justifyContent="center"
                                minHeight="250px"
                            >
                                <Paper
                                    elevation={0}
                                    sx={{
                                        maxWidth: "500px",
                                        width: "100%",
                                        display: "flex",
                                        flexDirection: "column",
                                        alignItems: "center",
                                        justifyContent: "center",
                                    }}
                                >
                                    <InfoOutlinedIcon color="primary" sx={{ fontSize: 45 }} />
                                    <Typography color="text.secondary" fontWeight="bold" mt={1} variant="h2">
                                        {t("customer:noAddOnsAvailable")}
                                    </Typography>
                                </Paper>
                            </Box>
                        )}
                    </Grid>
                    <Grid item md={6} xs={12}>
                        <CancellationCart
                            addedAddons={addedAddons}
                            onRemoveAddon={handleRemoveClick}
                            onConfirmCancellation={() => {
                                callWfeFacadeCancelationAddon({
                                    contactDetails,
                                    subscriptionsList: subscriptions?.collection ?? [],
                                    appointmentConfirmation: false,
                                    addonId: 0,
                                    serviceId: 0,
                                    setOrderIdForSchedule,
                                });
                            }}
                        />
                    </Grid>
                </Grid>
            )}
        </div>
    );
};

export default AddonsCancellation;
