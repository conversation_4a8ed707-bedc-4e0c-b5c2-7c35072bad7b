import { Box, Grid, Typography, Button, IconButton } from "@mui/material";
import { ICollectionAddOns } from "@services/subscription/accountId/interface/IAddOns";
import dayjs from "dayjs";
import DeleteIcon from "@mui/icons-material/Delete";

interface IProps {
    addedAddons: ICollectionAddOns[];
    onConfirmCancellation?: () => void;
    onRemoveAddon?: (addonId: number) => void;
}

const CancellationCart = ({ addedAddons, onConfirmCancellation, onRemoveAddon }: IProps) => {

    return (
        <Box sx={{ pb: 1, pt: 3, pl: 3, pr: 3 }}>
            <Typography color={"primary"} pb={3} textAlign={"center"} variant="h5">
                Addons a Cancelar
            </Typography>
            {addedAddons.length === 0 ? (
                <Box sx={{ textAlign: "center", py: 4 }}>
                    <Typography color="text.secondary" variant="body1">
                        No hay addons seleccionados para cancelar
                    </Typography>
                </Box>
            ) : (
                <>
                    {addedAddons.map((addon) => (

                        <Box
                            key={addon.id}
                            sx={{
                                mb: 1, 
                                p: 2,
                                border: "1px solid #e0e0e0",
                                borderRadius: 1,
                                backgroundColor: "#fff"
                            }}
                        >
                            <Grid container spacing={2}>
                                <Grid item xs={10} container alignItems="center">
                                    <Grid container spacing={2}>
                                        <Grid item xs={4}>
                                            <Typography color={"primary"} variant="subtitle1" fontWeight="bold">
                                                {addon.description}
                                            </Typography>
                                        </Grid>
                                        <Grid item xs={4}>
                                            <Typography variant="body2" color="text.secondary">
                                                ID: {addon.id}
                                            </Typography>
                                        </Grid>
                                        <Grid item xs={4}>
                                            <Typography variant="body2" color="text.secondary">
                                                Serial: {"SN1234567" + addon.id}
                                            </Typography>
                                        </Grid>
                                    </Grid>
                                </Grid>
                                <Grid item xs={2} sx={{ display: "flex", alignItems: "center", justifyContent: "center" }}>
                                    <IconButton
                                        color="error"
                                        onClick={() => onRemoveAddon?.(addon.id)}
                                        size="small"
                                    >
                                        <DeleteIcon />
                                    </IconButton>
                                </Grid>
                            </Grid>
                        </Box>
                    ))}
                    <Box sx={{ marginTop: 3, textAlign: "center" }}>
                        <Button
                            variant="contained"
                            color="error"
                            size="large"
                            onClick={onConfirmCancellation}
                            disabled={addedAddons.length === 0}
                            sx={{ minWidth: 200 }}
                        >
                            Confirmar Cancelación ({addedAddons.length})
                        </Button>
                    </Box>
                </>
            )}
        </Box>
    );
};

export default CancellationCart;
