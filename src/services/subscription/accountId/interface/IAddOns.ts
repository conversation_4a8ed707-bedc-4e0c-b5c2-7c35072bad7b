import { number } from "@translations/es/translation";

export interface IAddOns {
    collection: ICollectionAddOns[];
}

export interface ICollectionAddOns {
    accountId: null | string;
    activatedAt: string;
    charges: ICharge[];
    code: string;
    description: string;
    discounts: any[];
    equipmentId: null;
    id: number;
    includedEquipmentsCodes: any[];
    itemGroupCode: string;
    serviceId: number;
    status: string;
    terminatedAt: null;
    typeEquipmentReturn?: number;
}

interface ICharge {
    activatedAt: string;
    billingType: string;
    priceVatExcluded: number;
    priceVatIncluded: number;
    terminatedAt: null;
}

export interface ICollectionOffers {
    collection: IOfferAddOns[];
}

export interface IOfferAddOns {
    code: string;
    displayName: string;
    displayOrder: number;
    maxPerSubscription: number;
    incompatiblesItemGroupsCodes: any[];
    addons: IAddon[];
}

export interface IAddon {
    code: string;
    description: string;
    maxQuantity: number;
    prices: IPrice[];
    compatible: boolean;
    includedEquipmentsCodes: string[];
}

interface IPrice {
    price: number;
    billingType: string;
}

export interface IGetServicesGroups {
    code: string;
    displayName: string;
    equipmentType: null;
    serviceDomains: string[];
}

// <Detail Addons
export interface IDetailAddons {
    code: string;
    description: string;
    usages: IUsage[];
    charges: ICharges[];
    discounts: IDiscount[];
}

// Subinterfaces
export interface IUsage {
    description: string;
}

export interface ICharges {
    pricePlans: IPricePlan[];
}

export interface IPricePlan {
    defaultPrice: number;
}

export interface IDiscount {
    code: string;
    description: string;
    invoiceDescription: string;
    value: number;
    vatCode: IVatCode;
}

export interface IVatCode {
    rates: IRate[];
}

export interface IRate {
    validTo: string | null; // Puede ser null según el ejemplo proporcionado
}
